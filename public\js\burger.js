// Самый простой бургер-меню скрипт, который гарантированно работает
document.addEventListener('DOMContentLoaded', function() {
  console.log('Simple burger script loaded!');
  
  // Находим элементы
  const burger = document.getElementById('burgerBtn');
  const menu = document.getElementById('dropdownMenu');
  const body = document.body;
  const videoContainer = document.querySelector('.hero-video-container');
  const mobileHeader = document.querySelector('.mobile-header');
  
  // Проверяем, что они найдены
  if (!burger || !menu) {
    console.error('Burger button or menu not found!');
    return;
  }
  
  console.log('Burger and menu found, ready to work');
  
  // Функция для управления цветом бургера в зависимости от прокрутки
  function updateBurgerColor() {
    if (!videoContainer) {
      console.error('Video container not found!');
      return;
    }
    
    const videoContainerRect = videoContainer.getBoundingClientRect();
    const videoContainerBottom = videoContainerRect.bottom;
    
    // Если бургер находится в пределах видео-контейнера, то он белый
    // Иначе - черный
    if (videoContainerBottom > 0) {
      burger.classList.remove('dark');
      // Принудительно делаем полоски бургера белыми
      burger.querySelectorAll('span').forEach(span => {
        span.style.backgroundColor = '#fff !important';
        span.setAttribute('style', 'background-color: #fff !important');
      });
      // Убираем белый фон у хедера
      if (mobileHeader) {
        mobileHeader.classList.remove('scrolled');
      }
      console.log('Burger is white (in video area)');
    } else {
      burger.classList.add('dark');
      // Принудительно делаем полоски бургера черными
      burger.querySelectorAll('span').forEach(span => {
        span.style.backgroundColor = '#000 !important';
        span.setAttribute('style', 'background-color: #000 !important');
      });
      // Добавляем белый фон хедеру после прокрутки видео
      if (mobileHeader) {
        mobileHeader.classList.add('scrolled');
      }
      console.log('Burger is black (below video area)');
    }
  }
  
  // Вызываем функцию при загрузке страницы
  setTimeout(updateBurgerColor, 100); // Небольшая задержка для уверенности
  
  // Добавляем обработчик при прокрутке
  window.addEventListener('scroll', updateBurgerColor);
  
  // Принудительно вызываем обновление цвета при изменении размера окна
  window.addEventListener('resize', updateBurgerColor);
  
  // Проверяем, является ли устройство мобильным
  function isMobile() {
    return window.innerWidth <= 768;
  }

  function openMenu() {
    console.log('Opening menu');
    menu.classList.add('active');
    burger.classList.add('open');
    menu.style.display = 'flex';
  }

  function closeMenu() {
    console.log('Closing menu');
    menu.classList.remove('active');
    burger.classList.remove('open');
    menu.style.display = 'none';
    updateBurgerColor();
  }

  // Обработчик клика (только для мобильных)
  burger.addEventListener('click', function(event) {
    if (isMobile()) {
      event.stopPropagation();
      console.log('Burger clicked on mobile!');

      if (menu.classList.contains('active')) {
        closeMenu();
      } else {
        openMenu();
      }
    }
  });

  // Обработчики hover для десктопа
  function setupDesktopHover() {
    burger.addEventListener('mouseenter', function() {
      if (!isMobile()) {
        console.log('Burger hover on desktop!');
        openMenu();
      }
    });

    // Закрываем меню при уходе курсора с бургера и меню
    burger.addEventListener('mouseleave', function(e) {
      if (!isMobile()) {
        setTimeout(() => {
          if (!menu.matches(':hover') && !burger.matches(':hover')) {
            closeMenu();
          }
        }, 100);
      }
    });

    menu.addEventListener('mouseleave', function() {
      if (!isMobile()) {
        setTimeout(() => {
          if (!menu.matches(':hover') && !burger.matches(':hover')) {
            closeMenu();
          }
        }, 100);
      }
    });
  }

  // Инициализируем hover-обработчики
  setupDesktopHover();

  // Обрабатываем изменение размера окна
  window.addEventListener('resize', function() {
    if (isMobile()) {
      closeMenu(); // Закрываем меню при переходе на мобильную версию
    }
  });
  
  // Закрываем меню при клике вне его (только на мобильных)
  document.addEventListener('click', function(event) {
    if (isMobile() && menu.classList.contains('active') &&
        !menu.contains(event.target) &&
        event.target !== burger) {
      console.log('Clicked outside, closing menu');
      closeMenu();
    }
  });
  
  // Закрываем меню при клике на пункты меню
  const menuLinks = menu.querySelectorAll('a');
  menuLinks.forEach(link => {
    link.addEventListener('click', function() {
      console.log('Menu link clicked, closing menu');
      closeMenu();
    });
  });
  
  console.log('Simple burger script initialized');
}); 