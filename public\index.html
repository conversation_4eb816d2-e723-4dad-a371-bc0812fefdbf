<!DOCTYPE html>
<html lang="ru" style="margin: 0; padding: 0; overflow-x: hidden; width: 100%">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>SHMS Авто</title>
  <link rel="stylesheet" href="css/layout.css" />
  <link rel="stylesheet" href="css/styles.css?v=20250617b" />
  <link rel="stylesheet" href="css/footer.css" />
  <link rel="stylesheet" href="css/reviews.css" />
  <link rel="stylesheet" href="css/mobile-header.css" />
  <link rel="stylesheet" href="css/desktop-burger.css" />
  <link rel="stylesheet" href="css/stock.css" />
  <link rel="stylesheet" href="css/header-common.css" />
  <link rel="stylesheet" href="css/fiziev-modal.css" />
  <style>
    /* Глобальное устранение отступов */
    *,
    *::before,
    *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      margin: 0;
      padding: 0;
      overflow-x: hidden;
      width: 100%;
      position: relative;
    }

    /* Стили для карточек преимуществ */
    .benefit {
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .benefit:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    }

    .benefit::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: linear-gradient(90deg, #6f9aab, #3a5e6f);
      transform: scaleX(0);
      transform-origin: left;
      transition: transform 0.3s ease;
    }

    .benefit:hover::after {
      transform: scaleX(1);
    }

    .benefit--dark::after {
      background: linear-gradient(90deg, #bca0dc, #8a63b3);
    }

    /* Стили для иконок */
    .benefit__icon {
      display: block;
      margin: 0 auto 15px;
      width: 36px;
      height: 36px;
      object-fit: contain;
    }

    /* Стили для категорий автомобилей - полностью переработанные */
    .categories {
      padding: 30px 0;
      width: 100%;
      text-align: center;
      overflow: hidden;
    }

    .categories__title {
      text-align: center;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 25px;
      color: #333;
    }

    .categories__container {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .categories__list {
      display: flex;
      flex-direction: row;
      justify-content: center;
      flex-wrap: wrap;
      gap: 30px;
      width: 100%;
      box-sizing: border-box;
      padding: 10px 0;
    }

    .category {
      width: calc(33.33% - 20px);
      min-width: 250px;
      max-width: 300px;
      background: #fff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      display: flex;
      flex-direction: column;
      margin: 0 auto;
    }

    .category:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .category__image-container {
      width: 100%;
      height: 180px;
      overflow: hidden;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f8f8;
    }

    .category img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      transition: transform 0.3s ease;
    }

    .category:hover img {
      transform: scale(1.05);
    }

    .category__title-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      background-color: #fff;
      padding: 10px 5px;
      border-top: 1px solid #f0f0f0;
    }

    .category__title {
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      line-height: 1.2;
      color: #333;
    }

    /* Мобильные устройства */
    @media (max-width: 767px) {
      .categories {
        padding: 25px 0;
        position: relative;
        width: 100%;
        overflow: hidden;
        margin: 0 auto;
        text-align: center;
      }

      .categories__title {
        font-size: 20px;
        margin-bottom: 20px;
        padding: 0;
        text-align: center;
      }

      .categories__container {
        padding: 0;
        max-width: 100%;
        overflow-x: auto;
        scrollbar-width: none;
        -webkit-overflow-scrolling: touch;
        display: flex;
        justify-content: center;
      }

      .categories__container::-webkit-scrollbar {
        display: none;
      }

      .categories__list {
        display: inline-flex;
        flex-wrap: nowrap;
        justify-content: center;
        padding-bottom: 15px;
        scroll-snap-type: x mandatory;
        gap: 15px;
        margin: 0 auto;
        padding: 0 10px 15px;
        position: relative;
        scroll-padding: 15px;
        width: auto;
        text-align: center;
      }

      /* Добавляем контейнер для центрирования */
      .categories__list::before,
      .categories__list::after {
        content: "";
        width: 15px;
        flex: 0 0 auto;
      }

      .category {
        flex: 0 0 auto;
        min-width: 150px;
        margin-right: 0;
        scroll-snap-align: center;
        padding: 0;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
        border-radius: 12px;
        margin: 0 5px;
      }

      .category__image-container {
        height: 120px;
        padding: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .category img {
        max-height: 110px;
        object-fit: contain;
        margin-bottom: 0;
        border-radius: 8px;
      }

      .category__title-container {
        padding: 10px 5px;
        min-height: 40px;
      }

      .category__title {
        font-size: 0.95rem;
        margin: 0;
        line-height: 1.1;
      }
    }

    /* Устройства шириной до 600px */
    @media (max-width: 600px) {
      .categories {
        padding: 20px 0;
        text-align: center;
      }

      .categories__title {
        font-size: 18px;
        margin-bottom: 15px;
        font-weight: bold;
      }

      .categories__container {
        padding: 0;
        justify-content: center;
        display: flex;
      }

      .categories__list {
        gap: 10px;
        padding: 0 0 15px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        width: auto;
      }

      .categories__list::before,
      .categories__list::after {
        content: "";
        width: 8px;
        flex: 0 0 auto;
      }

      .category {
        width: 140px;
        min-width: 140px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
        margin: 0 5px;
        flex: 0 0 auto;
      }

      .category__image-container {
        height: 105px;
        padding: 5px;
        background-color: #f9f9f9;
      }

      .category img {
        max-height: 95px;
      }

      .category__title-container {
        padding: 8px 4px;
        min-height: 38px;
        background-color: #fff;
      }

      .category__title {
        font-size: 0.95rem;
        line-height: 1.15;
        color: #000;
        font-weight: 600;
      }
    }

    /* Очень маленькие экраны */
    @media (max-width: 480px) {
      .categories__title {
        font-size: 16px;
        margin-bottom: 12px;
      }

      .categories__list {
        gap: 8px;
        padding: 0 0 8px;
        justify-content: center;
      }

      .categories__container {
        display: flex;
        justify-content: center;
      }

      .category {
        width: 120px;
        min-width: 120px;
      }

      .category__image-container {
        height: 90px;
      }

      .category img {
        max-height: 80px;
      }

      .category__title-container {
        padding: 6px 3px;
        min-height: 40px;
      }

      .category__title {
        font-size: 0.85rem;
      }
    }

    /* Сверхмалые экраны */
    @media (max-width: 400px) {
      .category {
        width: 110px;
        min-width: 110px;
      }

      .category__image-container {
        height: 80px;
      }

      .category img {
        max-height: 75px;
      }

      .category__title-container {
        padding: 5px 3px;
      }

      .category__title {
        font-size: 0.8rem;
      }

      .categories__list {
        gap: 10px;
      }
    }

    /* Стили для модального окна статьи */
    .article-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 9999;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .article-modal.active {
      display: flex;
      opacity: 1;
    }

    .article-modal__content {
      background-color: #fff;
      padding: 30px;
      border-radius: 10px;
      max-width: 90%;
      width: 800px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
      position: relative;
      transform: translateY(-30px);
      transition: transform 0.3s ease;
      animation: modalAppear 0.4s forwards;
    }

    @keyframes modalAppear {
      from {
        opacity: 0;
        transform: translateY(-30px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .article-modal__close {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 28px;
      font-weight: bold;
      color: #333;
      cursor: pointer;
      transition: color 0.2s;
    }

    .article-modal__close:hover {
      color: #000;
    }

    .article-modal__title {
      font-size: 24px;
      margin-bottom: 20px;
      color: #333;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    .article-modal__content p {
      margin-bottom: 15px;
      line-height: 1.6;
    }

    /* Стили для списков в модальных окнах статей */
    .article-modal__content ul {
      margin: 15px 0;
      padding-left: 0;
      list-style: none;
    }

    .article-modal__content li {
      position: relative;
      margin-bottom: 8px;
      padding-left: 20px;
      line-height: 1.6;
    }

    .article-modal__content li::before {
      content: "•";
      position: absolute;
      left: 0;
      top: 0;
      color: #333;
      font-weight: bold;
    }

    /* Стили для модального окна выбора отзывов */
    .reviews-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 9999;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .reviews-modal.active {
      display: flex;
      opacity: 1;
    }

    .reviews-modal__content {
      background-color: #fff;
      padding: 30px;
      border-radius: 10px;
      max-width: 90%;
      width: 800px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
      position: relative;
      animation: modalAppear 0.4s forwards;
    }

    .reviews-modal__title {
      font-size: 24px;
      margin-bottom: 20px;
      color: #333;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    .reviews-modal__close {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 28px;
      font-weight: bold;
      color: #333;
      cursor: pointer;
      transition: color 0.2s;
    }

    .reviews-modal__close:hover {
      color: #000;
    }

    .reviews-modal__list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 20px;
    }

    .reviews-modal__item {
      cursor: pointer;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .reviews-modal__item:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .reviews-modal__item-img {
      width: 100%;
      height: 140px;
      object-fit: cover;
    }

    .reviews-modal__item-content {
      padding: 15px;
      background: #f9f9f9;
    }

    .reviews-modal__item-title {
      font-weight: bold;
      margin-bottom: 5px;
      font-size: 16px;
    }

    .reviews-modal__item-date {
      color: #888;
      font-size: 12px;
    }

    /* Стили для карточек отзывов */
    .review {
      cursor: pointer;
      transition: transform 0.3s ease;
    }

    .review:hover {
      transform: scale(1.02);
    }

    /* Стили для модального окна статьи отзыва */
    .review-article-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 9999;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .review-article-modal.active {
      display: flex;
      opacity: 1;
    }

    .review-article-modal__content {
      background-color: #fff;
      padding: 30px;
      border-radius: 10px;
      max-width: 90%;
      width: 800px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
      position: relative;
      animation: modalAppear 0.4s forwards;
    }

    .review-article-modal__header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .review-article-modal__image {
      width: 200px;
      height: 120px;
      object-fit: cover;
      border-radius: 8px;
      margin-right: 20px;
    }

    .review-article-modal__title {
      font-size: 24px;
      color: #333;
      margin-bottom: 5px;
    }

    .review-article-modal__date {
      color: #888;
      font-size: 14px;
    }

    .review-article-modal__body {
      line-height: 1.6;
    }

    .review-article-modal__close {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 28px;
      font-weight: bold;
      color: #333;
      cursor: pointer;
      transition: color 0.2s;
    }

    .review-article-modal__close:hover {
      color: #000;
    }

    /* Добавленные стили для меню */
    .dropdown-menu.active {
      display: flex !important;
      opacity: 1 !important;
      z-index: 2500 !important;
      pointer-events: auto !important;
      visibility: visible !important;
    }

    /* Стили для меню из resources */
    /* Dropdown menu */
    .dropdown-menu {
      position: fixed;
      top: 64px;
      left: 16px;
      min-width: 220px;
      max-width: 280px;
      width: calc(30% - 20px);
      background: #111;
      border-radius: 18px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
      padding: 32px 36px 24px 36px;
      z-index: 9500;
      display: none;
      flex-direction: column;
      gap: 18px;
      opacity: 0;
      pointer-events: none;
      transform: translateY(-20px) scale(0.98);
      transition: opacity 0.28s cubic-bezier(0.4, 1.3, 0.6, 1),
        transform 0.28s cubic-bezier(0.4, 1.3, 0.6, 1);
    }

    .dropdown-menu.active {
      display: flex;
      opacity: 1;
      pointer-events: auto;
      transform: translateY(0) scale(1);
    }

    .dropdown-menu a {
      color: #fff;
      font-size: 1.15rem;
      text-decoration: none;
      transition: color 0.18s;
      font-family: inherit;
    }

    .dropdown-menu a:hover {
      color: #b2c9d6;
    }

    /* Burger button */
    .burger {
      position: fixed;
      top: 24px;
      left: 24px;
      width: 40px;
      height: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: transparent;
      border: none;
      z-index: 9999;
      cursor: pointer;
      gap: 6px;
      border-radius: 5px;
    }

    .burger span {
      display: block !important;
      width: 32px !important;
      height: 4px !important;
      background-color: #fff !important;
      border-radius: 2px !important;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                  opacity 0.3s ease,
                  background-color 0.3s !important;
      transform-origin: center !important;
      position: relative !important;
    }

    /* Burger button animation - базовая для мобильных */
    .burger.open span:nth-child(1) {
      transform: translateY(6px) rotate(45deg);
    }

    .burger.open span:nth-child(2) {
      opacity: 0;
      transform: scale(0);
    }

    .burger.open span:nth-child(3) {
      transform: translateY(-6px) rotate(-45deg);
    }

    /* ПОЛНОЕ ИСПРАВЛЕНИЕ БУРГЕРА ДЛЯ ДЕСКТОПА - УБИВАЕМ ЖИРНУЮ ПОЛОСУ НАВСЕГДА */
    @media (min-width: 769px) {
      /* ПОЛНЫЙ СБРОС КНОПКИ БУРГЕРА */
      .burger {
        /* Убираем ВСЕ возможные источники жирной полосы */
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        background: rgba(0, 0, 0, 0.8) !important;
        overflow: hidden !important;
        position: relative !important;

        /* Размеры и позиционирование */
        width: 60px !important;
        height: 60px !important;
        border-radius: 12px !important;

        /* Flexbox для центрирования */
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 3px !important;
        padding: 0 !important;

        /* Убираем все псевдоэлементы */
        content: none !important;
      }

      /* УБИВАЕМ ВСЕ ПСЕВДОЭЛЕМЕНТЫ */
      .burger::before,
      .burger::after,
      .burger *::before,
      .burger *::after {
        display: none !important;
        content: none !important;
        width: 0 !important;
        height: 0 !important;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
        position: absolute !important;
        top: -9999px !important;
        left: -9999px !important;
      }

      /* ПРИНУДИТЕЛЬНО ПЕРЕЗАПИСЫВАЕМ ВСЕ ПОЛОСКИ - МАКСИМАЛЬНАЯ СПЕЦИФИЧНОСТЬ */
      .burger span:nth-child(1),
      .burger span:nth-child(2),
      .burger span:nth-child(3) {
        /* ПРИНУДИТЕЛЬНЫЕ РАЗМЕРЫ - УБИВАЕМ ЖИРНУЮ ПОЛОСУ */
        width: 20px !important;
        height: 2px !important;
        min-height: 2px !important;
        max-height: 2px !important;

        /* ПРИНУДИТЕЛЬНЫЙ ЦВЕТ И ФОРМА */
        background: #fff !important;
        background-color: #fff !important;
        border-radius: 1px !important;

        /* УБИРАЕМ ВСЕ ЛИШНЕЕ ПРИНУДИТЕЛЬНО */
        border: none !important;
        border-top: none !important;
        border-bottom: none !important;
        border-left: none !important;
        border-right: none !important;
        box-shadow: none !important;
        outline: none !important;
        margin: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding: 0 !important;

        /* ПОЗИЦИОНИРОВАНИЕ */
        display: block !important;
        position: relative !important;
        transform-origin: center center !important;

        /* ПЕРЕХОДЫ */
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important,
                    opacity 0.3s ease !important,
                    background-color 0.3s ease !important;

        /* УБИРАЕМ ПСЕВДОЭЛЕМЕНТЫ */
        content: none !important;
      }

      /* ДОПОЛНИТЕЛЬНАЯ ЗАЩИТА - КАЖДАЯ ПОЛОСКА ОТДЕЛЬНО */
      .burger span:nth-child(1) {
        width: 20px !important;
        height: 2px !important;
        background: #fff !important;
      }

      .burger span:nth-child(2) {
        width: 20px !important;
        height: 2px !important;
        background: #fff !important;
      }

      .burger span:nth-child(3) {
        width: 20px !important;
        height: 2px !important;
        background: #fff !important;
      }

      /* Убираем псевдоэлементы у полосок */
      .burger span:nth-child(1)::before,
      .burger span:nth-child(1)::after,
      .burger span:nth-child(2)::before,
      .burger span:nth-child(2)::after,
      .burger span:nth-child(3)::before,
      .burger span:nth-child(3)::after {
        display: none !important;
        content: none !important;
      }

      /* ИДЕАЛЬНАЯ АНИМАЦИЯ ПРЕВРАЩЕНИЯ В КРЕСТИК */
      .burger.open span:nth-child(1) {
        width: 20px !important;
        height: 2px !important;
        background: #fff !important;
        transform: translateY(5px) rotate(45deg) !important;
      }

      .burger.open span:nth-child(2) {
        width: 20px !important;
        height: 2px !important;
        background: #fff !important;
        opacity: 0 !important;
        transform: scale(0) !important;
      }

      .burger.open span:nth-child(3) {
        width: 20px !important;
        height: 2px !important;
        background: #fff !important;
        transform: translateY(-5px) rotate(-45deg) !important;
      }

      /* Hover эффект */
      .burger:hover {
        background: rgba(0, 0, 0, 0.9) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4) !important;
      }

      /* Текст "Меню" */
      .burger-text {
        display: block !important;
        color: #fff !important;
        font-size: 10px !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        margin-top: 4px !important;
        line-height: 1 !important;
        border: none !important;
        background: none !important;
        box-shadow: none !important;
      }
    }

    /* Dark class for burger button - with higher specificity */
    .burger.dark span {
      background-color: #000 !important;
    }

    .burger.dark {
      color: #000 !important;
    }

    /* Стили для надписи "Меню" */
    .burger-text {
      display: none !important; /* Скрываем на мобильных по умолчанию */
    }

    /* ДУБЛИРУЮЩИЙ БЛОК УДАЛЕН - ВСЕ СТИЛИ БУРГЕРА ТЕПЕРЬ В ЕДИНОМ БЛОКЕ ВЫШЕ */

    /* Styles for the empty cars message and loading */
    .stock__cards .loading,
    .stock__cards .no-cars,
    .stock__cards .error {
      width: 100%;
      padding: 30px;
      text-align: center;
      font-size: 18px;
      color: #777;
      background-color: #f8f8f8;
      border-radius: 10px;
      margin: 20px 0;
    }

    .stock__cards .error {
      color: #d9534f;
      background-color: #fdf7f7;
      border: 1px solid #f5e4e4;
    }

    .stock__cards .loading {
      position: relative;
      color: #555;
    }

    /* Улучшенный стиль для карточек автомобилей на мобильных устройствах */
    @media (max-width: 768px) {
      .stock {
        padding: 20px 0 !important;
      }

      .stock__title {
        padding: 0 15px !important;
        margin-bottom: 15px !important;
        text-align: center !important;
        font-size: 22px !important;
      }

      /* Улучшаем контейнер карточек */
      .stock-cards,
      .stock__cards {
        display: flex !important;
        flex-direction: column !important;
        gap: 15px !important;
        width: 100% !important;
        padding: 0 15px !important;
        box-sizing: border-box !important;
        align-items: center !important;
        /* Центрирование карточек */
      }

      /* Стилизуем карточку машины для мобильных */
      .car-card {
        width: 100% !important;
        max-width: 400px !important;
        /* Ограничиваем максимальную ширину */
        margin: 0 auto !important;
        height: auto !important;
        border-radius: 12px !important;
        overflow: hidden !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
      }

      .car-card__image {
        height: 260px !important;
        /* Точно как в stock.css */
        overflow: hidden !important;
        position: relative !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      .car-card__image img {
        width: 100% !important;
        height: 100% !important;
        object-fit: fill !important;
        /* Центрируем изображение */
        transition: transform 0.3s ease !important;
      }

      .car-card__info {
        padding: 15px 18px !important;
        /* Увеличиваем внутренние отступы */
        background-color: #fff !important;
      }

      .car-card__title {
        font-size: 18px !important;
        font-weight: 600 !important;
        margin-bottom: 5px !important;
      }

      .car-card__price {
        font-size: 18px !important;
        font-weight: bold !important;
      }

      .car-card__specs {
        margin-top: 8px !important;
        font-size: 14px !important;
      }

      .car-card__specs-row {
        padding: 10px 15px !important;
        background-color: #f8f8f8 !important;
        display: flex !important;
        justify-content: space-between !important;
      }

      .car-card__spec {
        text-align: center !important;
      }

      .car-card__header {
        padding: 12px 15px !important;
        background-color: #f8f8f8 !important;
      }

      /* Специфичные стили для маленьких экранов */
      @media (max-width: 480px) {
        .car-card {
          max-width: 95% !important;
          /* Немного уже на совсем маленьких экранах */
        }

        .car-card__image {
          height: 200px !important;
        }

        .car-card__title {
          font-size: 17px !important;
        }

        .car-card__price {
          font-size: 16px !important;
        }
      }
    }

    .stock__cards .loading:after {
      content: "...";
      position: absolute;
      display: inline-block;
      animation: loadingDots 1.5s infinite;
      width: 12px;
      text-align: left;
    }

    @keyframes loadingDots {
      0% {
        content: ".";
      }

      33% {
        content: "..";
      }

      66% {
        content: "...";
      }
    }

    /* Улучшаем только отображение фотографий автомобилей */
    .car-card__image {
      position: relative;
      height: 260px;
      overflow: hidden;
      background-color: #f8f8f8;
    }

    .car-card__image img {
      width: 100%;
      height: 100%;
      object-fit: fill;
      transition: none;
    }

    /* Убираем блокировку скролла при открытом меню */
    body.menu-open {
      overflow: auto !important;
      /* Принудительно разрешаем скролл */
      position: static !important;
      /* Отменяем position:fixed */
    }

    /* Стиль для затемнения фона при открытом меню */
    body.menu-open::after {
      /* Убираем затемнение при открытом меню */
      display: none !important;
      opacity: 0 !important;
      content: none !important;
    }

    /* Mobile Header */
    .mobile-header {
      display: flex !important;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: 60px;
      background: transparent;
      /* По умолчанию прозрачный */
      z-index: 9000;
      padding: 0 20px;
      align-items: center;
      justify-content: space-between;
      box-shadow: none;
      /* Убираем тень */
      transition: background-color 0.3s ease;
    }

    /* Полностью убираем белую полосу сверху */
    body,
    html {
      margin: 0;
      padding: 0;
      width: 100%;
      overflow-x: hidden;
    }

    /* Исправляем отображение на мобильных устройствах */
    @media (max-width: 768px) {
      body {
        padding: 0 !important;
        margin: 0 !important;
      }

      /* Фиксируем отображение видео на весь экран */
      .hero {
        height: 100vh !important;
        overflow: hidden;
        position: relative;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      /* Белый фон для хедера только после прокрутки */
      .mobile-header {
        height: 60px;
        background-color: transparent !important;
        box-shadow: none !important;
      }

      .mobile-header.scrolled {
        background-color: rgba(255, 255, 255, 0.95) !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
      }
    }

    /* Бургер всегда виден */
    .burger {
      display: flex !important;
      position: relative;
      background: transparent;
      /* Убираем оранжевый фон */
      z-index: 9999;
    }

    /* Цвет бургера на мобильных зависит от прокрутки */
    @media (max-width: 768px) {
      .mobile-header.scrolled .burger span {
        background-color: #000 !important;
      }

      /* Принудительно скрываем надпись на мобильных */
      .burger-text {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        width: 0 !important;
        overflow: hidden !important;
      }

      /* Возвращаем стандартные размеры бургера на мобильных */
      .burger {
        width: 40px !important;
        height: 40px !important;
        flex-direction: column !important;
        gap: 6px !important;
        padding: 0 !important;
        background: transparent !important;
        border: none !important;
        border-radius: 5px !important;
      }
    }

    /* Улучшаем отображение видео на мобильных устройствах */
    @media (max-width: 768px) {

      /* Простой контейнер с нормальными размерами */
      .hero {
        height: 100vh !important;
        width: 100% !important;
        position: relative !important;
        overflow: hidden !important;
      }

      /* Оставляем контейнеру video-container исходные размеры */
      .hero-video-container,
      .video-container {
        position: relative !important;
        width: 100% !important;
        height: 100% !important;
        overflow: hidden !important;
      }

      /* Центрируем и масштабируем само видео */
      .hero__bg,
      .video-bg {
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        min-width: 100% !important;
        min-height: 100% !important;
        width: auto !important;
        height: auto !important;
        object-fit: cover !important;
        object-position: center center !important;
        /* Дополнительно центрируем контент */
      }

      /* Отдельное исправление для iOS */
      @supports (-webkit-touch-callout: none) {

        .hero__bg,
        .video-bg {
          position: absolute !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          -webkit-transform: translate(-50%, -50%) !important;
          width: auto !important;
          height: auto !important;
          min-width: 100% !important;
          min-height: 100% !important;
          object-position: center 35% !important;
          /* Для iOS сдвигаем немного вверх */
        }
      }
    }

    /* Дополнительная настройка для маленьких экранов телефонов */
    @media (max-width: 480px) {

      .hero__bg,
      .video-bg {
        object-position: center 40% !important;
        /* Сдвигаем центр для вертикальных телефонов */
      }
    }

    /* Пустой отступ чтобы контент не уходил под хедер */
    .mobile-header-spacer {
      display: block !important;
      height: 60px;
    }

    /* Улучшенная адаптивность для мобильных */
    @media (max-width: 768px) {

      /* Увеличиваем отступы между секциями */
      main>section {
        margin-bottom: 30px !important;
      }

      /* Уменьшаем высоту хедера на мобильном */
      .mobile-header {
        height: 60px !important;
      }

      .mobile-header-spacer {
        height: 60px !important;
      }

      /* Улучшаем отображение выпадающего меню */
      .dropdown-menu {
        top: 60px !important;
        max-height: 85vh;
        overflow-y: auto;
        padding: 25px;
        border-radius: 12px;
      }

      /* Улучшаем отображение преимуществ */
      .benefits__list {
        flex-direction: column !important;
        gap: 15px !important;
        padding: 0 15px !important;
      }

      .benefit {
        max-width: 100% !important;
      }

      /* Улучшаем отступы в футере */
      .footer {
        padding: 20px 15px !important;
      }

      .footer__info {
        flex-direction: column !important;
        gap: 25px !important;
      }

      .footer__left,
      .footer__right {
        width: 100% !important;
      }

      /* Корректировка для поиска */
      .search__form {
        width: 90% !important;
        max-width: none !important;
        margin: 0 auto !important;
      }
    }

    /* Дополнительные правки для очень маленьких экранов */
    @media (max-width: 480px) {
      .burger {
        top: 12px !important;
        left: 12px !important;
      }

      .dropdown-menu {
        padding: 20px !important;
      }

      .dropdown-menu a {
        font-size: 16px !important;
        margin-bottom: 8px !important;
      }
    }

    /* Mobile-first подход */
    @media (max-width: 768px) {
      .mobile-header {
        display: flex;
      }

      .mobile-header-spacer {
        display: block;
      }

      /* Бургер всегда виден на мобильном */
      .burger {
        display: flex;
        position: relative;
        top: auto;
        left: auto;
      }

      .dropdown-menu {
        display: none;
        /* начальное состояние */
      }

      .dropdown-menu.active {
        display: flex;
      }
    }

    /* Добавляем общие стили для мобильной адаптации */
    @media (max-width: 768px) {
      body {
        padding: 0;
        font-size: 15px;
      }

      section {
        padding: 20px 10px !important;
        margin: 10px 0 !important;
      }

      .hero {
        height: 60vh !important;
      }

      h2,
      h3 {
        font-size: 22px !important;
        margin-bottom: 15px !important;
      }

      /* Основные контейнеры */
      .container,
      .benefits__list,
      .reviews__list {
        padding: 0 10px !important;
        gap: 15px !important;
      }

      /* Улучшаем карточки машин для мобильных устройств */
      .stock__cards {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
        padding: 0 5px !important;
      }

      .car-card {
        max-width: 100% !important;
        margin: 0 auto !important;
        padding-bottom: 32px !important;
      }

      /* Улучшаем расположение текста "В наличии в Москве" */
      .car-card__price-block {
        width: 50% !important;
      }

      .car-card__in-stock {
        text-align: center !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
      }

      /* Исправление выпадающего меню */
      .dropdown-menu {
        top: 90px !important;
        left: 10px !important;
        width: calc(100% - 20px) !important;
        max-width: none !important;
        z-index: 9999 !important;
      }
    }

    /* Дополнительная адаптация для маленьких экранов */
    @media (max-width: 480px) {
      .benefit {
        padding: 15px !important;
        margin-bottom: 10px !important;
      }

      .review {
        height: 220px !important;
      }

      .reviews__list {
        flex-direction: column !important;
      }

      .reviews__btn {
        min-width: 140px !important;
        padding: 10px 15px !important;
      }
    }

    /* Стили для очень маленьких экранов */
    @media (max-width: 380px) {
      body {
        font-size: 14px !important;
      }

      h2,
      h3 {
        font-size: 20px !important;
      }

      .search__input {
        font-size: 14px !important;
        padding: 10px !important;
      }
    }

    /* Общие стили для видео (на всех устройствах) */
    .hero {
      height: 100vh;
      width: 100%;
      max-width: 100%;
      overflow: hidden;
      position: relative;
      margin: 0;
      padding: 0;
    }

    .video-container,
    .hero-video-container {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      margin: 0;
      padding: 0;
    }

    /* Фокус на самом видео - центрирование и масштабирование */
    .video-bg,
    .hero__bg {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      min-width: 100%;
      min-height: 100%;
      width: auto;
      height: auto;
      object-fit: cover;
      z-index: 0;
    }

    /* Восстановление стилей для оверлея */
    .hero__overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
      z-index: 1;
    }

    /* Вставить в <style> внутри <head> (лучше после всех других hero/video-bg стилей) */
    .hero__bg--desktop,
    .hero__bg--mobile,
    .hero__bg--widescreen {
      display: none;
    }

    @media (max-width: 767px) {
      .hero__bg--mobile {
        display: block;
      }
    }

    @media (min-width: 768px) and (max-aspect-ratio: 16/9) {
      .hero__bg--desktop {
        display: block;
      }
    }

    @media (min-width: 1200px) and (min-aspect-ratio: 8/5) and (max-aspect-ratio: 16/10) {
      .hero__bg--widescreen {
        display: block;
      }

      .hero__bg--desktop {
        display: none;
      }
    }

    /* Safari/macOS/iOS only: корректируем только widescreen видео */
    @media not all and (min-resolution: 0.001dpcm) {
      @supports (-webkit-touch-callout: none) {
        .hero__bg--widescreen {
          object-fit: cover !important;
          width: 100vw !important;
          height: 100vh !important;
          min-width: 100vw !important;
          min-height: 100vh !important;
          max-width: none !important;
          max-height: none !important;
          object-position: center 80% !important;
          transform: scale(0.95);
          transition: transform 0.3s;
        }
      }
    }

    /* Скрываем логотип на компьютерах, оставляем на мобильных */
    @media (min-width: 769px) {
      .mobile-header__logo {
        display: none !important;
      }
    }
  </style>
</head>

<body>
  <nav class="dropdown-menu" id="dropdownMenu">
    <a href="stock.html">Авто в наличии</a>
    <a href="order.html">Авто под заказ</a>
    <a href="contacts.html">Наши контакты</a>
    <a href="feedback-mockup.html">Обратная связь</a>
  </nav>

  <!-- Hero Section -->
  <section class="hero">
    <div class="hero__overlay"></div>
    <div class="video-container hero-video-container">
      <!-- Десктопное видео -->
      <video class="hero__bg video-bg hero__bg--desktop" src="/assets/img/backgrounds/hero-desktop.mp4" autoplay loop
        muted playsinline preload="auto" poster="/assets/img/icons/logo-shms-black.png"></video>
      <!-- Мобильное видео (укажите свой src) -->
      <video class="hero__bg video-bg hero__bg--mobile" src="/assets/img/backgrounds/hero-mobile.mp4" autoplay loop
        muted playsinline preload="auto" poster="/assets/img/icons/logo-shms-black.png"></video>
      <!-- Видео для 16:10 (например, 2560x1600) -->
      <video class="hero__bg video-bg hero__bg--widescreen" src="/Img/k1_k1_k1 bbbbb.mp4" autoplay loop muted
        playsinline preload="auto" poster="/assets/img/icons/logo-shms-black.png"></video>
    </div>

  </section>

  <!-- Mobile Header -->
  <header class="mobile-header">
    <button class="burger" id="burgerBtn" aria-label="Открыть меню">
      <span></span>
      <span></span>
      <span></span>
      <span class="burger-text">Меню</span>
    </button>

  </header>

  <!-- Пустой раздел для отступа -->
  <div class="mobile-header-spacer"></div>

  <!-- Brands -->
  <section class="brands">
    <div class="brands-container">
      <div class="brands__grid">
        <div class="brand">
          <img src="/assets/img/logos/logo-mercedes.png" alt="Mercedes-Benz" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-aston-martin.png" alt="Aston Martin" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-audi.png" alt="Audi" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-rols.png" alt="Rolls-Royce" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-lamborghini.png" alt="Lamborghini" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-tesla.png" alt="Tesla" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-jaguar.png" alt="Jaguar" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-ferrari.png" alt="Ferrari" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-bmw.png" alt="BMW" width="120" height="60" />
        </div>
        <div class="brand">
          <img src="/assets/img/logos/logo-porsche.png" alt="Porsche" width="120" height="60" />
        </div>
      </div>
    </div>
  </section>

  <!-- Cars in Stock -->
  <main>
    <section class="stock">
      <div class="stock-container">
        <h2 class="stock__title">В наличии в Москве</h2>
        <div class="stock-cards stock__cards">
          <!-- Car cards will be dynamically loaded here -->
        </div>
      </div>
    </section>

    <!-- Benefits -->
    <section class="benefits">
      <div class="benefits-container">
        <h3 class="benefits__title">
          Самые выгодные условия сотрудничества -<br />с проверенной
          экспортной компанией
        </h3>
        <div class="benefits__list">
          <div class="benefit" id="benefit-vat">
            <img src="/assets/img/icons/icon-card.png" class="benefit__icon" alt="Карта" />
            <div class="benefit__title">Возвращаем НДС</div>
            <div class="benefit__desc">
              Специальный сервис для компаний и юридических лиц
            </div>
          </div>
          <div class="benefit benefit--dark" id="benefit-crypto">
            <img src="/assets/img/icons/icon-bts.png" class="benefit__icon" alt="Bitcoin" />
            <div class="benefit__title">Возможна оплата криптовалютой</div>
            <div class="benefit__desc">Принимаем оплату в USDT и Bitcoin</div>
          </div>
          <div class="benefit" id="benefit-fast">
            <img src="/assets/img/icons/icon-copy.png" class="benefit__icon" alt="Дата" />
            <div class="benefit__title">Быстро и качественно</div>
            <div class="benefit__desc">
              Короткие сроки доставки и авто без пробега
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Navigation Buttons -->
    <section class="search">
      <div class="search-container">
        <div class="navigation-buttons-wrapper">
          <!-- Кнопка "Авто в наличии" -->
          <a href="stock.html" class="nav-btn nav-btn--stock">
            <div class="nav-btn__content">
              <span class="nav-btn__text">Авто в наличии</span>
            </div>
          </a>

          <!-- Кнопка "Авто под заказ" -->
          <a href="order.html" class="nav-btn nav-btn--order">
            <div class="nav-btn__content">
              <span class="nav-btn__text">Авто под заказ</span>
            </div>
          </a>
        </div>
      </div>
    </section>

    <!-- Car Categories -->
    <section class="categories">
      <div class="categories-container">
        <h2 class="categories__title">Категории автомобилей</h2>
        <div class="categories__container">
          <!-- не забудь перенести: кнопки категорий теперь перенаправляют на order.html с параметрами -->
          <div class="categories__list">
            <a href="order.html?category=business" class="category" data-class="business">
              <div class="category__image-container">
                <img src="/assets/img/cars/maibax.png" alt="Бизнес-класс" />
              </div>
              <div class="category__title-container">
                <div class="category__title">Бизнес-класс</div>
              </div>
            </a>
            <a href="order.html?category=sport" class="category" data-class="sport">
              <div class="category__image-container">
                <img src="/assets/img/cars/maclaren.png" alt="Спортивные авто" />
              </div>
              <div class="category__title-container">
                <div class="category__title">Спортивные Авто</div>
              </div>
            </a>
            <a href="order.html?category=suv" class="category" data-class="suv">
              <div class="category__image-container">
                <img src="/assets/img/cars/bmw.jpg" alt="SUV" />
              </div>
              <div class="category__title-container">
                <div class="category__title">SUV</div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Reviews -->
    <section class="reviews">
      <div class="reviews-container">
        <h2 class="reviews__title">Обзоры и отзывы от наших клиентов</h2>
        <div class="reviews__btn-group">
          <button class="reviews__btn" id="readReviewsBtn">
            Читать отзывы
          </button>
          <button class="reviews__btn reviews__btn--secondary">
            Оставить отзыв
          </button>
        </div>
        <div class="reviews__list">
          <!-- Отзыв 1 -->
          <div class="review" id="review-1" style="background-image: url('/assets/img/cars/ford.png')">
            <div class="review__gradient">
              <div class="review__date">3 марта 2025 г.</div>
              <button class="review__arrow" aria-label="Подробнее">
                <svg width="48" height="36" viewBox="0 0 48 36" fill="none">
                  <rect x="0" y="0" width="48" height="36" rx="12" fill="black" />
                  <path d="M14 18H34" stroke="white" stroke-width="3.5" stroke-linecap="round" />
                  <path d="M29.5 13.5L34 18L29.5 22.5" stroke="white" stroke-width="3.5" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
              </button>
              <div class="review__content">
                <div class="review__title">Ford Mustang GTD</div>
                <div class="review__desc">
                  Самый дорогой и быстрый из серии, уникальный дизайн,
                  интерактивный дисплей, система и Акроповичем – еще громче!
                </div>
              </div>
            </div>
          </div>

          <!-- Отзыв 2 -->
          <div class="review" id="review-2" style="background-image: url('/assets/img/cars/audi-2.jpeg')">
            <div class="review__gradient">
              <div class="review__date">23 июля 2023 г.</div>
              <button class="review__arrow" aria-label="Подробнее">
                <svg width="48" height="36" viewBox="0 0 48 36" fill="none">
                  <rect x="0" y="0" width="48" height="36" rx="12" fill="black" />
                  <path d="M14 18H34" stroke="white" stroke-width="3.5" stroke-linecap="round" />
                  <path d="M29.5 13.5L34 18L29.5 22.5" stroke="white" stroke-width="3.5" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
              </button>
              <div class="review__content">
                <div class="review__title">Audi RS6 Avante Perfomance</div>
                <div class="review__desc">
                  Самый быстрый серийник и его уникальный дизайн, требует
                  личного присутствия, чтобы влюбить в себя за секунды.
                </div>
              </div>
            </div>
          </div>

          <!-- Отзыв 3 -->
          <div class="review" id="review-3" style="background-image: url('/assets/img/cars/gelik.webp')">
            <div class="review__gradient">
              <div class="review__date">13 декабря 2024 г.</div>
              <button class="review__arrow" aria-label="Подробнее">
                <svg width="48" height="36" viewBox="0 0 48 36" fill="none">
                  <rect x="0" y="0" width="48" height="36" rx="12" fill="black" />
                  <path d="M14 18H34" stroke="white" stroke-width="3.5" stroke-linecap="round" />
                  <path d="M29.5 13.5L34 18L29.5 22.5" stroke="white" stroke-width="3.5" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
              </button>
              <div class="review__content">
                <div class="review__title">Mercedes-Benz G63 AMG</div>
                <div class="review__desc">
                  Последний из Гелендвагенов – как мы привезли монстра без
                  пробега в Россию, и почему именно эту модель.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact -->
    <section class="contact">
      <div class="contact-container">
        <h2 class="contact__title"> Свяжитесь с нами </h2>
        <a href="feedback-mockup.html" class="contact__btn" aria-label="Связаться">
          <svg width="48" height="36" viewBox="0 0 48 36" fill="none">
            <rect x="0" y="0" width="48" height="36" rx="12" fill="black" />
            <path d="M14 18H34" stroke="white" stroke-width="3.5" stroke-linecap="round" />
            <path d="M29.5 13.5L34 18L29.5 22.5" stroke="white" stroke-width="3.5" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </a>
        <div class="contact__desc">Выберите способ связи по ссылке</div>
        <div class="contact__divider"></div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer__info">
      <div class="footer__left">
        <div class="footer__logo">
          <img src="/assets/img/icons/logo-shms.png" alt="SHMS" class="footer__logo-img" />
        </div>
        <div class="footer__address">
          Наш адрес:<br />
          Москва,<br />
          Пресненская набережная 12<br />
          Башня "Федерация"<br />
          12 этаж, офис К2
        </div>
      </div>
      <div class="footer__right">
        <nav class="footer__nav">
          <a href="order.html">Под заказ</a>
          <a href="contacts.html">Контакты</a>
          <a href="stock.html">Авто в наличии</a>
        </nav>
        <div class="footer__requisites">
          <div class="footer__requisites-title">Реквизиты организации</div>
          <div class="footer__requisites-info">
            ИП Шамаев Мансур Махмудович<br />
            ИНН 201578554480, ОГРН 324200000020490<br />
          </div>
        </div>
      </div>
    </div>
    <div class="footer__copyright">© 2025 Все права защищены</div>
  </footer>

  <!-- Модальное окно для отзыва -->
  <div class="modal" id="reviewModal">
    <div class="modal__content">
      <span class="modal__close" id="closeReviewModal">&times;</span>
      <h3>Оставить отзыв</h3>
      <form id="reviewForm">
        <input type="hidden" name="formType" value="review" />
        <input type="text" name="name" placeholder="Ваше имя" required />
        <input type="text" name="contact" placeholder="Телефон или Email" required />
        <textarea name="message" placeholder="Ваш отзыв" required></textarea>
        <label class="checkbox-label">
          <input type="checkbox" name="consent" required />
          Я согласен на обработку персональных данных
        </label>
        <button type="submit">Отправить</button>
      </form>
    </div>
    <div class="modal__success" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 300px; text-align: center; background: white; padding: 2rem; border-radius: 12px; font-size: 1.2rem; font-weight: 600; color: green; box-shadow: 0 8px 32px rgba(0,0,0,0.18); z-index: 1001;">
      <h3 style="margin: 0; text-align: center; color: green; font-size: 1.2rem;">Спасибо за отзыв!</h3>
    </div>
  </div>

  <!-- Cookie Consent Modal -->
  <div class="cookie-modal" id="cookieModal" hidden>
    <div class="cookie-modal__content">
      <h3>Использование файлов cookie</h3>
      <p>
        Мы используем файлы cookie для улучшения работы сайта. Продолжая
        пользоваться сайтом, вы соглашаетесь с нашей
        <a href="#" target="_blank">политикой использования cookie</a>.
      </p>
      <button id="acceptCookies" class="cookie-modal__btn">Согласен</button>
    </div>
  </div>

  <!-- Модальные окна для статей -->
  <div class="article-modal" id="modal-vat">
    <div class="article-modal__content">
      <span class="article-modal__close">&times;</span>
      <h2 class="article-modal__title">Возвращаем НДС</h2>
      <div class="article-modal__body">
        <p>
          В SHMS Auto мы предлагаем специальный сервис для компаний и
          юридических лиц, который позволяет вернуть НДС при покупке
          автомобиля.
        </p>
        <p>
          Наши специалисты помогут вам оформить все необходимые документы и
          проведут полное сопровождение процесса возврата НДС, что позволит
          вам существенно сэкономить на покупке.
        </p>
        <p>
          Мы работаем с компаниями любого размера и предлагаем индивидуальные
          условия сотрудничества.
        </p>
        <p><strong>Преимущества нашего сервиса:</strong></p>
        <ul>
          <li>Полное юридическое сопровождение</li>
          <li>Быстрое оформление документов</li>
          <li>Гарантия возврата НДС</li>
          <li>Индивидуальный подход к каждому клиенту</li>
        </ul>
      </div>
    </div>
  </div>

  <div class="article-modal" id="modal-crypto">
    <div class="article-modal__content">
      <span class="article-modal__close">&times;</span>
      <h2 class="article-modal__title">Оплата криптовалютой</h2>
      <div class="article-modal__body">
        <p>
          SHMS Auto предлагает современный и удобный способ оплаты – с помощью
          криптовалюты. Мы принимаем оплату в USDT и Bitcoin.
        </p>
        <p>
          Это позволяет нашим клиентам проводить транзакции быстро, безопасно
          и с минимальными комиссиями.
        </p>
        <p><strong>Преимущества оплаты криптовалютой:</strong></p>
        <ul>
          <li>Быстрые транзакции без задержек</li>
          <li>Низкие комиссии</li>
          <li>Высокий уровень безопасности</li>
          <li>Удобство для международных клиентов</li>
        </ul>
        <p>
          Наши специалисты помогут вам провести оплату и ответят на все
          вопросы, связанные с использованием криптовалюты.
        </p>
      </div>
    </div>
  </div>

  <div class="article-modal" id="modal-fast">
    <div class="article-modal__content">
      <span class="article-modal__close">&times;</span>
      <h2 class="article-modal__title">Быстро и качественно</h2>
      <div class="article-modal__body">
        <p>
          В SHMS Auto мы ценим время наших клиентов и гарантируем короткие
          сроки доставки автомобилей.
        </p>
        <p>
          Все автомобили, которые мы предлагаем, имеют нулевой пробег и
          проходят тщательную проверку перед доставкой.
        </p>
        <p><strong>Наши преимущества:</strong></p>
        <ul>
          <li>Доставка автомобилей в кратчайшие сроки</li>
          <li>Только новые автомобили без пробега</li>
          <li>Полная техническая проверка перед доставкой</li>
          <li>Оформление всех необходимых документов</li>
        </ul>
        <p>
          Мы гарантируем, что ваш автомобиль будет доставлен в идеальном
          состоянии и в срок.
        </p>
      </div>
    </div>
  </div>

  <!-- Модальное окно выбора отзывов -->
  <div class="reviews-modal" id="reviewsModal">
    <div class="reviews-modal__content">
      <span class="reviews-modal__close">&times;</span>
      <h2 class="reviews-modal__title">Выберите отзыв для прочтения</h2>
      <div class="reviews-modal__list">
        <!-- Элемент списка отзывов 1 -->
        <div class="reviews-modal__item" data-review-id="1">
          <img src="/assets/img/cars/ford.png" alt="Ford Mustang GTD" class="reviews-modal__item-img" />
          <div class="reviews-modal__item-content">
            <div class="reviews-modal__item-title">Ford Mustang GTD</div>
            <div class="reviews-modal__item-date">3 марта 2025 г.</div>
          </div>
        </div>
        <!-- Элемент списка отзывов 2 -->
        <div class="reviews-modal__item" data-review-id="2">
          <img src="/assets/img/cars/audi-2.jpeg" alt="Audi RS6 Avante Perfomance" class="reviews-modal__item-img" />
          <div class="reviews-modal__item-content">
            <div class="reviews-modal__item-title">
              Audi RS6 Avante Perfomance
            </div>
            <div class="reviews-modal__item-date">23 июля 2023 г.</div>
          </div>
        </div>
        <!-- Элемент списка отзывов 3 -->
        <div class="reviews-modal__item" data-review-id="3">
          <img src="/assets/img/cars/gelik.webp" alt="Mercedes-Benz G63 AMG" class="reviews-modal__item-img" />
          <div class="reviews-modal__item-content">
            <div class="reviews-modal__item-title">Mercedes-Benz G63 AMG</div>
            <div class="reviews-modal__item-date">13 декабря 2024 г.</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Модальные окна для отзывов -->
  <div class="review-article-modal" id="review-article-1">
    <div class="review-article-modal__content">
      <span class="review-article-modal__close">&times;</span>
      <div class="review-article-modal__header">
        <img src="/assets/img/cars/ford.png" alt="Ford Mustang GTD" class="review-article-modal__image" />
        <div>
          <h2 class="review-article-modal__title">Ford Mustang GTD</h2>
          <div class="review-article-modal__date">3 марта 2025 г.</div>
          <div class="review-article-modal__author">
            Автор: Команда SHMS Auto
          </div>
          <div class="review-article-modal__car">
            Автомобиль: Ford Mustang GTD
          </div>
        </div>
      </div>
      <div class="review-article-modal__body">
        <p>
          Самый дорогой и быстрый из серии, уникальный дизайн, интерактивный
          дисплей, система и Акроповичем – еще громче!
        </p>
        <p>
          Ford Mustang GTD — это не просто автомобиль, это настоящее
          произведение инженерного искусства. С момента своего появления на
          рынке, эта модель привлекла внимание всех ценителей скорости и
          мощности.
        </p>
        <p>
          Оснащенный мощным двигателем, Mustang GTD способен разгоняться до
          100 км/ч всего за несколько секунд. Уникальный дизайн кузова не
          только придает автомобилю агрессивный вид, но и улучшает
          аэродинамические характеристики.
        </p>
        <p>
          Интерактивный дисплей, установленный в салоне, предоставляет
          водителю всю необходимую информацию и позволяет управлять различными
          системами автомобиля одним касанием.
        </p>
        <p>
          Особого внимания заслуживает выхлопная система Akrapovič, которая не
          только улучшает производительность двигателя, но и создает
          неповторимый звук, который оценит каждый автолюбитель.
        </p>
      </div>
    </div>
  </div>

  <div class="review-article-modal" id="review-article-2">
    <div class="review-article-modal__content">
      <span class="review-article-modal__close">&times;</span>
      <div class="review-article-modal__header">
        <img src="/assets/img/cars/audi-2.jpeg" alt="Audi RS6 Avante Perfomance" class="review-article-modal__image" />
        <div>
          <h2 class="review-article-modal__title">
            Audi RS6 Avante Perfomance
          </h2>
          <div class="review-article-modal__date">23 июля 2023 г.</div>
          <div class="review-article-modal__author">Автор: Алексей М.</div>
          <div class="review-article-modal__car">
            Автомобиль: Audi RS6 Avante Perfomance
          </div>
        </div>
      </div>
      <div class="review-article-modal__body">
        <p>
          Самый быстрый серийник и его уникальный дизайн, требует личного
          присутствия, чтобы влюбить в себя за секунды.
        </p>
        <p>
          Audi RS6 Avante Performance — это воплощение скорости и
          элегантности. Этот автомобиль сочетает в себе мощь спортивного купе
          и практичность универсала.
        </p>
        <p>
          Благодаря инновационным технологиям и высококачественным материалам,
          RS6 Avante Performance обеспечивает непревзойденный комфорт и
          безопасность даже на высоких скоростях.
        </p>
        <p>
          Уникальный дизайн этого автомобиля привлекает внимание на дороге и
          вызывает восхищение у всех, кто его видит. Однако, чтобы
          по-настоящему оценить все достоинства RS6 Avante Performance,
          необходимо увидеть его вживую.
        </p>
        <p>
          Этот автомобиль не просто средство передвижения, это настоящее
          произведение искусства, которое способно влюбить в себя с первого
          взгляда.
        </p>
      </div>
    </div>
  </div>

  <div class="review-article-modal" id="review-article-3">
    <div class="review-article-modal__content">
      <span class="review-article-modal__close">&times;</span>
      <div class="review-article-modal__header">
        <img src="/assets/img/cars/gelik.webp" alt="Mercedes-Benz G63 AMG" class="review-article-modal__image" />
        <div>
          <h2 class="review-article-modal__title">Mercedes-Benz G63 AMG</h2>
          <div class="review-article-modal__date">13 декабря 2024 г.</div>
          <div class="review-article-modal__author">Автор: Михаил Д.</div>
          <div class="review-article-modal__car">
            Автомобиль: Mercedes-Benz G63 AMG
          </div>
        </div>
      </div>
      <div class="review-article-modal__body">
        <p>
          Последний из Гелендвагенов – как мы привезли монстра без пробега в
          Россию, и почему именно эту модель.
        </p>
        <p>
          Mercedes-Benz G63 AMG — это легенда среди внедорожников. С момента
          своего появления на рынке, эта модель завоевала сердца многих
          автолюбителей по всему миру.
        </p>
        <p>
          Благодаря своей надежности, проходимости и мощности, G63 AMG стал
          одним из самых желанных автомобилей в своем классе. Несмотря на свой
          брутальный внешний вид, этот внедорожник обеспечивает высокий
          уровень комфорта и безопасности.
        </p>
        <p>
          Мы гордимся тем, что смогли привезти в Россию этот автомобиль без
          пробега. Это было непростой задачей, но результат стоил всех усилий.
        </p>
        <p>
          Почему именно эта модель? Потому что G63 AMG — это не просто
          автомобиль, это символ статуса и успеха, который никогда не выйдет
          из моды.
        </p>
      </div>
    </div>
  </div>



  <script src="js/reviews.js"></script>
  <script src="js/reviews-loader.js"></script>
  <script src="js/burger.js"></script>
  <script src="js/index.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function () {
      // Дополнительный код для исправления скролла
      document.body.style.overflow = "auto"; // Всегда разрешаем скролл

      // Открытие модального окна при клике на карточку отзыва
      document.querySelectorAll(".review").forEach(function (reviewCard) {
        reviewCard.addEventListener("click", function () {
          const reviewId = this.id.split("-")[1];
          const modalId = `review-article-${reviewId}`;
          document.getElementById(modalId).classList.add("active");
        });
      });

      // Закрытие модальных окон отзывов при клике на крестик
      document
        .querySelectorAll(".review-article-modal__close")
        .forEach(function (closeBtn) {
          closeBtn.addEventListener("click", function () {
            this.closest(".review-article-modal").classList.remove("active");
          });
        });

      // Закрытие модальных окон отзывов при клике вне содержимого
      document
        .querySelectorAll(".review-article-modal")
        .forEach(function (modal) {
          modal.addEventListener("click", function (e) {
            if (e.target === this) {
              this.classList.remove("active");
            }
          });
        });

      // Открытие модального окна со списком отзывов
      document
        .getElementById("readReviewsBtn")
        .addEventListener("click", function () {
          document.getElementById("reviewsModal").classList.add("active");
        });

      // Закрытие модального окна со списком отзывов
      document
        .querySelector(".reviews-modal__close")
        .addEventListener("click", function () {
          document.getElementById("reviewsModal").classList.remove("active");
        });

      // Открытие модального окна с конкретным отзывом при клике на элемент в списке
      document
        .querySelectorAll(".reviews-modal__item")
        .forEach(function (item) {
          item.addEventListener("click", function () {
            const reviewId = this.getAttribute("data-review-id");
            document
              .getElementById("reviewsModal")
              .classList.remove("active");
            document
              .getElementById(`review-article-${reviewId}`)
              .classList.add("active");
          });
        });
    });

    document.querySelector(".reviews__btn--secondary").onclick = function () {
      document.getElementById("reviewModal").classList.add("active");
    };
    document.getElementById("closeReviewModal").onclick = function () {
      document.getElementById("reviewModal").classList.remove("active");
    };
    document.getElementById("reviewForm").onsubmit = async function (e) {
      e.preventDefault();
      console.log("Форма отправляется...");

      const form = e.target;
      const data = {
        name: form.name.value,
        contact: form.contact.value,
        message: form.message.value,
        formType: form.formType.value,
      };

      console.log("Данные формы:", data);

      try {
        const res = await fetch("/api/send-review", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        });

        console.log("Ответ сервера:", res.status);

        if (res.ok) {
          console.log("Успешно отправлено!");
          form.style.display = "none";
          document.querySelector(".modal__success").style.display = "block";
        } else {
          console.error("Ошибка сервера:", res.status);
          alert("Ошибка при отправке формы. Попробуйте еще раз.");
        }
      } catch (error) {
        console.error("Ошибка:", error);
        alert("Ошибка соединения. Проверьте интернет-подключение.");
      }
    };

    if (!localStorage.getItem("cookieAccepted")) {
      document.getElementById("cookieModal").hidden = false;
    } else {
      document.getElementById("cookieModal").hidden = true;
    }
    document.getElementById("acceptCookies").onclick = function () {
      localStorage.setItem("cookieAccepted", "true");
      document.getElementById("cookieModal").hidden = true;
    };

    // Обработчики для карточек преимуществ
    document
      .getElementById("benefit-vat")
      .addEventListener("click", function () {
        document.getElementById("modal-vat").classList.add("active");
      });

    document
      .getElementById("benefit-crypto")
      .addEventListener("click", function () {
        document.getElementById("modal-crypto").classList.add("active");
      });

    document
      .getElementById("benefit-fast")
      .addEventListener("click", function () {
        document.getElementById("modal-fast").classList.add("active");
      });

    // Закрытие модальных окон статей
    document
      .querySelectorAll(".article-modal__close")
      .forEach(function (closeBtn) {
        closeBtn.addEventListener("click", function () {
          this.closest(".article-modal").classList.remove("active");
        });
      });

    // Закрытие модальных окон статей при клике вне содержимого
    document.querySelectorAll(".article-modal").forEach(function (modal) {
      modal.addEventListener("click", function (e) {
        if (e.target === this) {
          this.classList.remove("active");
        }
      });
    });
  </script>

  <!-- Подключение модального окна Физиева -->
  <script src="js/fiziev-modal.js"></script>
</body>

</html>