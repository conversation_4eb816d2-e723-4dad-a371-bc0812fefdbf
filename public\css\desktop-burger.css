/* Стили для бургер-меню, скопированные из оригинального стиля */
@media (min-width: 769px) {
  /* Dropdown menu */
  .dropdown-menu {
    position: fixed;
    top: 64px;
    left: 16px;
    min-width: 260px;
    background: #111;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    padding: 32px 36px 24px 36px;
    z-index: 1500;
    display: none;
    flex-direction: column;
    gap: 18px;
    opacity: 0;
    pointer-events: none;
    transform: translateY(-20px) scale(0.98);
    transition: opacity 0.28s cubic-bezier(.4,1.3,.6,1), transform 0.28s cubic-bezier(.4,1.3,.6,1);
  }

  .dropdown-menu.active {
    display: flex;
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0) scale(1);
  }

  .dropdown-menu a {
    color: #fff;
    font-size: 1.15rem;
    text-decoration: none;
    transition: color 0.18s;
    font-family: inherit;
  }

  .dropdown-menu a:hover {
    color: #b2c9d6;
  }

  /* Burger button */
  .burger {
    position: fixed;
    top: 24px;
    left: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: none;
    border: none;
    z-index: 2000;
    cursor: pointer;
    gap: 6px;
  }

  /* СТИЛИ ПОЛОСОК ОТКЛЮЧЕНЫ - ВСЕ УПРАВЛЯЕТСЯ ИЗ index.html */

  /* Burger button animation */
  .burger.open span:nth-child(1) {
    transform: translateY(10px) rotate(45deg);
  }

  .burger.open span:nth-child(2) {
    opacity: 0;
  }

  .burger.open span:nth-child(3) {
    transform: translateY(-10px) rotate(-45deg);
  }

  .burger.dark span {
    background-color: #000;
  }

  .burger.dark {
    color: #000;
  }

  body.menu-open {
    overflow: hidden;
  }
} 