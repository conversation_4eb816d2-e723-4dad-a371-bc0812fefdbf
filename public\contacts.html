<!DOCTYPE html>
<html lang="ru">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Наши контакты | SHMS</title>
  <link rel="stylesheet" href="css/contacts.css" />
  <link rel="stylesheet" href="css/footer.css" />
  <link rel="stylesheet" href="css/mobile-header.css" />
  <link rel="stylesheet" href="css/hero.css?v=14" />
  <link rel="stylesheet" href="css/header-common.css" />
  <link rel="stylesheet" href="css/fiziev-modal.css" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
  <style>
    /* Стили для модального окна согласия */
    .consent-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 9999;
      justify-content: center;
      align-items: center;
    }

    .consent-modal__content {
      background-color: #fff;
      padding: 30px;
      border-radius: 10px;
      max-width: 90%;
      width: 500px;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
      position: relative;
      animation: modalAppear 0.3s ease-out;
    }

    @keyframes modalAppear {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .consent-modal__title {
      font-size: 1.4rem;
      font-weight: bold;
      margin-bottom: 20px;
      color: #333;
    }

    .consent-modal__text {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 25px;
      color: #555;
    }

    .consent-modal__buttons {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
    }

    .consent-modal__button {
      padding: 12px 25px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-weight: bold;
      font-size: 1.1rem;
      transition: all 0.2s ease;
    }

    .consent-modal__button--agree {
      background-color: #000;
      color: #fff;
    }

    .consent-modal__button--agree:hover {
      background-color: #333;
    }

    .consent-modal__button--cancel {
      background-color: #e0e0e0;
      color: #333;
    }

    .consent-modal__button--cancel:hover {
      background-color: #ccc;
    }

    /* Стили для карточек контактов, чтобы было понятно, что они кликабельны */
    .contacts-info__card {
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .contacts-info__card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .contacts-info__phone-buttons {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    .phone-btn {
      background: #f5f8fa;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.3rem;
      color: #55707b;
      cursor: pointer;
      transition: background 0.2s, color 0.2s;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    }

    .phone-btn:hover {
      background: #e0eaf1;
      color: #1a4d6a;
    }

    .phone-btn--call {
      color: #1a4d6a;
    }

    .phone-btn--tg {
      color: #0088cc;
    }

    .phone-btn--wa {
      color: #25d366;
    }

    /* Адаптивные стили для мобильных устройств */
    @media (max-width: 700px) {
      .contacts-info__phone-buttons {
        justify-content: center;
        margin-top: 15px;
        gap: 15px;
      }

      .phone-btn {
        width: 45px;
        height: 45px;
        font-size: 1.4rem;
      }

      .contacts-info__card--phone {
        text-align: center;
      }
    }
  </style>
</head>

<body>
  <!-- Mobile Header -->
  <header class="mobile-header">
    <button class="burger" id="burgerBtn" aria-label="Открыть меню">
      <span></span>
      <span></span>
      <span></span>
    </button>
    <a href="index.html" class="mobile-header__logo">
      <img src="/Img/logo.png" alt="SHMS" class="mobile-header__logo-img">
    </a>
  </header>

  <!-- Пустой раздел для отступа -->
  <div class="mobile-header-spacer"></div>

  <!-- Mobile Menu -->
  <nav class="dropdown-menu" id="dropdownMenu">
    <a href="stock.html">Авто в наличии</a>
    <a href="order.html">Авто под заказ</a>
    <a href="contacts.html">Наши контакты</a>
    <a href="feedback-mockup.html">Обратная связь</a>
  </nav>

  <!-- Hero / Title Section -->
  <section class="hero-stock">
    <div class="hero-header-group">
      <div class="header__left">
        <a href="#" class="header__back-link" onclick="window.history.back(); return false;">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M5 12L12 19M5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          Назад
        </a>
      </div>
      <div class="header__center-group">
        <nav class="header__nav">
          <a href="index.html" class="header__logo-link"><img src="/Img/logo.png" alt="SHMS"
              class="header__logo-img"></a>
          <div class="header__nav-links">
            <a href="stock.html" class="header__nav-link">Авто в наличии</a>
            <a href="order.html" class="header__nav-link">Заказать авто</a>
            <a href="contacts.html" class="header__nav-link active">Наши контакты</a>
            <a href="feedback-mockup.html" class="header__nav-link">Обратная связь</a>
          </div>
        </nav>
      </div>
    </div>
  </section>
  <section class="hero-stock hero-stock--custom hero-stock--large">
    <div class="hero-stock__visual">
      <div class="hero-stock__center">
        <h1 class="hero-stock__title-main">Наши контакты</h1>
        <p class="hero-stock__subtitle-main">
          Наша команда всегда на связи для решения ваших вопросов и сервиса
        </p>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <main>
    <section class="contacts-info">
      <h2 class="contacts-info__heading">Свяжитесь с нами:</h2>
      <div class="contacts-info__grid">
        <div class="contacts-info__card contacts-info__card--address" id="address-card">
          <strong>Московский<br />головной офис</strong>
          <p id="office-address">
            Пресненская наб., 12,<br />
            Москва, 123317<br />
            12 этаж, офис к2
          </p>
          <div class="copy-tooltip" id="copy-tooltip">Адрес скопирован!</div>
        </div>
        <div class="contacts-info__card contacts-info__card--phone" id="phone-card">
          <strong>Телефон</strong>
          <span class="contacts-info__phone">+7 (936) 220-04-60</span>
          <span class="contacts-info__desc">Директор вип-офиса</span>
          <div class="contacts-info__phone-buttons">
            <button class="phone-btn phone-btn--call" title="Позвонить">
              <i class="fas fa-phone"></i>
            </button>
            <button class="phone-btn phone-btn--tg" title="Telegram">
              <i class="fab fa-telegram-plane"></i>
            </button>
            <button class="phone-btn phone-btn--wa" title="WhatsApp">
              <i class="fab fa-whatsapp"></i>
            </button>
          </div>
          <div class="copy-tooltip" id="phone-tooltip">
            Нажмите для звонка
          </div>
        </div>
        <div class="contacts-info__card contacts-info__card--email" id="email-card">
          <strong>Email</strong>
          <span class="contacts-info__email"><EMAIL></span>
          <span class="contacts-info__desc">Оф.канал связи</span>
          <div class="copy-tooltip" id="email-tooltip">Email скопирован!</div>
        </div>
      </div>
    </section>

    <!-- Социальные сети -->
    <section class="social-media">
      <h2 class="social-media__title">Мы в социальных сетях</h2>
      <div class="social-media__grid">
        <a href="https://youtube.com/@shmsauto" target="_blank" class="social-media__card">
          <i class="fab fa-youtube social-media__icon" style="color: #ff0000"></i>
          <div class="social-media__name">YouTube</div>
          <div class="social-media__handle">@shmsauto</div>
        </a>
        <a href="https://t.me/shmsauto" target="_blank" class="social-media__card">
          <i class="fab fa-telegram social-media__icon" style="color: #0088cc"></i>
          <div class="social-media__name">Telegram</div>
          <div class="social-media__handle">@shmsauto</div>
        </a>
      </div>
    </section>

    <section class="contacts-map" id="map-block">
      <!-- Карта-заглушка -->
      <iframe src="https://yandex.ru/map-widget/v1/?ll=37.539183%2C55.749473&z=17&pt=37.539183,55.749473,pm2dgl"
        frameborder="0" title="Карта" allowfullscreen class="contacts-map__iframe"></iframe>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer__info">
      <div class="footer__left">
        <div class="footer__logo">
          <img src="/assets/img/icons/logo-shms.png" alt="SHMS" class="footer__logo-img" />
        </div>
        <div class="footer__address">
          Наш адрес:<br />
          Москва,<br />
          Пресненская набережная 12<br />
          Башня "Федерация"<br />
          12 этаж, офис К2
        </div>
      </div>
      <div class="footer__right">
        <nav class="footer__nav">
          <a href="order.html">Под заказ</a>
          <a href="contacts.html">Контакты</a>
          <a href="stock.html">Авто в наличии</a>
        </nav>
        <div class="footer__requisites">
          <div class="footer__requisites-title">Реквизиты организации</div>
          <div class="footer__requisites-info">
            ИП Шамаев Мансур Махмудович<br />
            ИНН 201578554480, ОГРН 324200000020490<br />
          </div>
        </div>
      </div>
    </div>
    <div class="footer__copyright">© 2025 Все права защищены</div>
  </footer>

  <!-- Модальное окно согласия на обработку персональных данных -->
  <div class="consent-modal" id="consentModal">
    <div class="consent-modal__content">
      <div class="consent-modal__title">Согласие на обработку данных</div>
      <div class="consent-modal__text">
        Нажимая кнопку "Согласен", вы разрешаете нам использовать ваши
        контактные данные для связи с вами. Мы гарантируем конфиденциальность
        вашей информации.
      </div>
      <div class="consent-modal__buttons">
        <button class="consent-modal__button consent-modal__button--cancel" id="cancelConsent">
          Отмена
        </button>
        <button class="consent-modal__button consent-modal__button--agree" id="agreeConsent">
          Согласен
        </button>
      </div>
    </div>
  </div>

  <script src="js/menu.js"></script>
  <script>
    // Проверка наличия всех необходимых элементов
    document.addEventListener("DOMContentLoaded", function () {
      console.log("DOM загружен, проверяем элементы...");

      // Проверяем наличие элементов
      const elements = {
        addressCard: document.getElementById("address-card"),
        addressText: document.getElementById("office-address"),
        copyTooltip: document.getElementById("copy-tooltip"),
        phoneCard: document.getElementById("phone-card"),
        phoneTooltip: document.getElementById("phone-tooltip"),
        emailCard: document.getElementById("email-card"),
        emailTooltip: document.getElementById("email-tooltip"),
        consentModal: document.getElementById("consentModal"),
        agreeConsentBtn: document.getElementById("agreeConsent"),
        cancelConsentBtn: document.getElementById("cancelConsent"),
      };

      // Выводим статус каждого элемента
      for (const [name, element] of Object.entries(elements)) {
        console.log(`Элемент ${name}: ${element ? "найден" : "НЕ НАЙДЕН"}`);
      }

      // Если все элементы найдены, инициализируем обработчики
      if (Object.values(elements).every((el) => el !== null)) {
        console.log("Все элементы найдены, инициализируем обработчики");
        initializeHandlers(elements);
      } else {
        console.error("Не все элементы найдены, проверьте HTML-структуру");
      }
    });

    // Функция инициализации обработчиков
    function initializeHandlers(elements) {
      // Переменные для хранения текущего действия
      let currentAction = "";
      let actionParams = {};

      // --- ДОБАВЛЕНО: обработка кнопок на всех устройствах ---
      // Кнопка звонка
      const callBtn = document.querySelector(".phone-btn--call");
      if (callBtn) {
        callBtn.addEventListener("click", function (e) {
          e.stopPropagation();
          window.location.href = "tel:+79362200460";
        });
      }

      // Кнопка Telegram
      const tgBtn = document.querySelector(".phone-btn--tg");
      if (tgBtn) {
        tgBtn.addEventListener("click", function (e) {
          e.stopPropagation();
          window.open("https://t.me/ShmsautoAli", "_blank");
        });
      }

      // Кнопка WhatsApp
      const waBtn = document.querySelector(".phone-btn--wa");
      if (waBtn) {
        waBtn.addEventListener("click", function (e) {
          e.stopPropagation();
          window.open("https://wa.me/79362200460", "_blank");
        });
      }
      // --- КОНЕЦ ДОБАВЛЕНИЯ ---

      // Функция показа модального окна согласия
      function showConsentModal() {
        console.log("Показываем модальное окно согласия");
        elements.consentModal.style.display = "flex";

        // Проверяем, действительно ли отображается модальное окно
        setTimeout(() => {
          const isVisible = elements.consentModal.style.display === "flex";
          console.log(`Модальное окно ${isVisible ? "видимо" : "НЕ ВИДИМО"}`);
          console.log(
            "Текущие стили модального окна:",
            `display: ${window.getComputedStyle(elements.consentModal).display
            }, `,
            `opacity: ${window.getComputedStyle(elements.consentModal).opacity
            }, `,
            `visibility: ${window.getComputedStyle(elements.consentModal).visibility
            }`
          );

          // Если окно не отображается, попробуем принудительно показать его
          if (!isVisible) {
            console.log("Принудительно показываем модальное окно");
            elements.consentModal.style.cssText =
              "display: flex !important; opacity: 1 !important; visibility: visible !important; z-index: 9999 !important;";
          }
        }, 100);
      }

      // Копирование адреса при клике на карточку - без модального окна
      elements.addressCard.addEventListener("click", function () {
        console.log("Клик по карточке адреса - копируем напрямую");
        const address =
          "Пресненская наб., 12, Москва, 123317, 12 этаж, офис к2";
        navigator.clipboard.writeText(address);
        elements.copyTooltip.style.opacity = 1;
        setTimeout(() => {
          elements.copyTooltip.style.opacity = 0;
        }, 1200);
      });

      // Обработка карточки с телефоном - с модальным окном
      elements.phoneCard.addEventListener("click", function () {
        console.log("Клик по карточке телефона");
        currentAction = "phoneAction";
        showConsentModal();
      });

      // Обработка карточки с email - с модальным окном
      elements.emailCard.addEventListener("click", function () {
        console.log("Клик по карточке email");
        currentAction = "emailAction";
        const email = "<EMAIL>";
        const subject = "Запрос информации об автомобиле";
        const body =
          "Здравствуйте, команда SHMS!\n\nЯ заинтересован(а) в получении информации о ";

        actionParams = { email, subject, body };
        showConsentModal();
      });

      // Обработчик кнопки "Согласен"
      elements.agreeConsentBtn.addEventListener("click", function () {
        console.log('Нажата кнопка "Согласен"');
        elements.consentModal.style.display = "none";

        // Выполняем действие в зависимости от текущего контекста
        switch (currentAction) {
          case "phoneAction":
            console.log("Действие с телефоном");
            const phoneNumber = "+79362200460";
            if (
              /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
                navigator.userAgent
              )
            ) {
              window.location.href = "tel:" + phoneNumber;
            } else {
              navigator.clipboard.writeText(phoneNumber);
              elements.phoneTooltip.textContent = "Номер скопирован!";
              elements.phoneTooltip.style.opacity = 1;
              setTimeout(() => {
                elements.phoneTooltip.style.opacity = 0;
                setTimeout(() => {
                  elements.phoneTooltip.textContent = "Нажмите для звонка";
                }, 300);
              }, 1200);
            }
            break;

          case "emailAction":
            console.log("Действие с email");
            const { email, subject, body } = actionParams;
            if (
              /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
                navigator.userAgent
              )
            ) {
              window.location.href = `mailto:${email}?subject=${encodeURIComponent(
                subject
              )}&body=${encodeURIComponent(body)}`;
            } else {
              navigator.clipboard.writeText(email);
              elements.emailTooltip.style.opacity = 1;
              setTimeout(() => {
                elements.emailTooltip.style.opacity = 0;
              }, 1200);
            }
            break;
        }
      });

      // Обработчик кнопки "Отмена"
      elements.cancelConsentBtn.addEventListener("click", function () {
        console.log('Нажата кнопка "Отмена"');
        elements.consentModal.style.display = "none";
      });

      // Закрытие модального окна по клику вне его содержимого
      document.addEventListener("click", function (e) {
        if (
          elements.consentModal.style.display === "flex" &&
          !elements.consentModal
            .querySelector(".consent-modal__content")
            .contains(e.target)
        ) {
          console.log("Клик вне модального окна, закрываем");
          elements.consentModal.style.display = "none";
        }
      });
    }

    // Меню обрабатывается через menu.js
  </script>

  <!-- Подключение модального окна Физиева -->
  <script src="js/fiziev-modal.js"></script>
</body>

</html>