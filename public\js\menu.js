document.addEventListener('DOMContentLoaded', function() {
  console.log('Burger menu initialization starting...');
  const burger = document.getElementById('burgerBtn');
  const menu = document.getElementById('dropdownMenu');
  console.log('Burger button found:', !!burger);
  console.log('Dropdown menu found:', !!menu);
  const hero = document.querySelector('.hero-stock') || document.querySelector('.hero');
  const body = document.body;
  const mobileHeader = document.querySelector('.mobile-header');
  const textLogo = document.querySelector('.text-logo');

  if (!burger) {
    console.error('ОШИБКА: Бургер-кнопка с ID burgerBtn не найдена!');
    return;
  }
  
  if (!menu) {
    console.error('ОШИБКА: Меню с ID dropdownMenu не найдено!');
    return;
  }

  function closeMenu() {
    menu.classList.remove('active');
    burger.classList.remove('open');
    body.classList.remove('menu-open');
    console.log('Menu closed');
  }

  function openMenu() {
    menu.classList.add('active');
    burger.classList.add('open');
    body.classList.add('menu-open');
    console.log('Menu opened');
  }

  burger.style.border = "2px solid red"; // Временная подсветка для отладки
  
  // Проверяем, является ли устройство мобильным
  function isMobile() {
    return window.innerWidth <= 768;
  }

  // Обработчик клика (только для мобильных)
  burger.addEventListener('click', function(e) {
    if (isMobile()) {
      console.log('Burger clicked on mobile!');
      e.stopPropagation();
      if (menu.classList.contains('active')) {
        closeMenu();
      } else {
        openMenu();
      }
    }
  });

  // Обработчики hover для десктопа
  function setupDesktopHover() {
    burger.addEventListener('mouseenter', function() {
      if (!isMobile()) {
        console.log('Burger hover on desktop!');
        openMenu();
      }
    });

    // Закрываем меню при уходе курсора с бургера и меню
    burger.addEventListener('mouseleave', function(e) {
      if (!isMobile()) {
        setTimeout(() => {
          if (!menu.matches(':hover') && !burger.matches(':hover')) {
            closeMenu();
          }
        }, 100);
      }
    });

    menu.addEventListener('mouseleave', function() {
      if (!isMobile()) {
        setTimeout(() => {
          if (!menu.matches(':hover') && !burger.matches(':hover')) {
            closeMenu();
          }
        }, 100);
      }
    });
  }

  // Инициализируем hover-обработчики
  setupDesktopHover();

  // Обрабатываем изменение размера окна
  window.addEventListener('resize', function() {
    if (isMobile()) {
      closeMenu(); // Закрываем меню при переходе на мобильную версию
    }
  });

  // Закрытие по клику вне меню и вне кнопки
  document.addEventListener('click', function(e) {
    if (menu.classList.contains('active') && !menu.contains(e.target) && e.target !== burger) {
      closeMenu();
    }
  });

  // Навигация по меню
  const menuLinks = menu.querySelectorAll('a');
  menuLinks.forEach(link => {
    link.addEventListener('click', function() {
      closeMenu();
    });
  });

  // Меняем цвет хедера при скролле
  function updateHeader() {
    if (!hero) return;
    
    // На index.html меняем цвет хедера при скролле
    if (window.location.pathname.includes('index.html') || window.location.pathname === '/' || window.location.pathname.endsWith('/')) {
      const heroRect = hero.getBoundingClientRect();
      if (heroRect.bottom <= 0) {
        mobileHeader.style.background = '#fff';
        // Для текстового лого ничего не меняем
      } else {
        mobileHeader.style.background = 'transparent';
        // Для текстового лого ничего не меняем
      }
    } else {
      // На остальных страницах держим белый хедер
      mobileHeader.style.background = '#fff';
    }
  }

  window.addEventListener('scroll', updateHeader);
  updateHeader();
  console.log('Burger menu initialized');
}); 