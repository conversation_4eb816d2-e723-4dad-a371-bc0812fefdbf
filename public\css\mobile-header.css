/* Моб<PERSON><PERSON><PERSON>ный заголовок для всех страниц */
.mobile-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  padding: 0;
  z-index: 1000;
}

.mobile-header__logo {
  position: static;
  margin: 0 auto;
  width: auto;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-decoration: none;
}

.mobile-header__logo-img {
  height: 40px;
  width: auto;
  object-fit: contain;
}

.mobile-header-spacer {
  display: none;
  height: 60px;
  width: 100%;
}

/* Бургер меню */
.burger {
  position: absolute;
  top: 50%;
  left: 20px;
  width: 32px;
  height: 32px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  background: none;
  border: none;
  z-index: 2000;
  cursor: pointer;
  gap: 4px;
}

.burger span {
  display: block;
  width: 22px;
  height: 2px;
  background: #000;
  border-radius: 1px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  transform-origin: center;
  position: relative;
}

/* Скрываем текст "Меню" на мобильных устройствах */
.burger-text {
  display: none !important;
}

.burger-text {
  display: none; /* Скрываем текст на мобильных */
}

/* Анимация бургера - базовая для мобильных */
.burger.open span:nth-child(1) {
  transform: translateY(6px) rotate(45deg);
}
.burger.open span:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}
.burger.open span:nth-child(3) {
  transform: translateY(-6px) rotate(-45deg);
}

/* УБИВАЕМ ЖИРНУЮ ПОЛОСУ НАВСЕГДА - ТОЛЬКО для десктопа */
@media (min-width: 769px) {
  /* УБИВАЕМ ВСЕ ЛИШНЕЕ */
  .burger::before,
  .burger::after {
    display: none !important;
    content: "" !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* ТОЛЬКО 3 ПОЛОСКИ - УБИВАЕМ ВСЕ ОСТАЛЬНОЕ */
  .burger span:nth-child(1) {
    width: 20px !important;
    height: 2px !important;
    background: #fff !important;
    margin: 1px 0 !important;
    border-radius: 1px !important;
    transform-origin: center !important;
    transition: transform 0.3s ease !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
  }

  .burger span:nth-child(2) {
    width: 20px !important;
    height: 2px !important;
    background: #fff !important;
    margin: 1px 0 !important;
    border-radius: 1px !important;
    transform-origin: center !important;
    transition: opacity 0.3s ease !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
  }

  .burger span:nth-child(3) {
    width: 20px !important;
    height: 2px !important;
    background: #fff !important;
    margin: 1px 0 !important;
    border-radius: 1px !important;
    transform-origin: center !important;
    transition: transform 0.3s ease !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* ЧИСТАЯ АНИМАЦИЯ БЕЗ БАГОВ */
  .burger.open span:nth-child(1) {
    transform: translateY(3px) rotate(45deg) !important;
  }

  .burger.open span:nth-child(2) {
    opacity: 0 !important;
  }

  .burger.open span:nth-child(3) {
    transform: translateY(-3px) rotate(-45deg) !important;
  }
}

/* Выпадающее меню */
.dropdown-menu {
  position: fixed;
  top: 60px;
  left: 0;
  width: 100%;
  background: #fff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  padding: 20px;
  z-index: 1000;
  display: none;
  flex-direction: column;
  gap: 15px;
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s, transform 0.2s;
}

.dropdown-menu.active {
  display: flex;
  opacity: 1;
  transform: translateY(0);
}

.dropdown-menu a {
  color: #111;
  font-size: 1.1rem;
  text-decoration: none;
  padding: 10px 15px;
  border-radius: 5px;
  transition: background 0.2s;
}

.dropdown-menu a:hover {
  background: #f0f0f0;
}

/* Медиа-запрос для мобильного отображения */
@media (max-width: 768px) {
  .burger {
    display: flex;
    width: 32px !important;
    height: 32px !important;
    flex-direction: column !important;
    gap: 4px !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
    border-radius: 0 !important;
  }

  .burger-text {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  .mobile-header {
    display: flex;
  }

  .mobile-header-spacer {
    display: block;
  }
}

/* Скрываем на десктопе (кроме бургера и меню) */
@media (min-width: 769px) {
  .mobile-header,
  .mobile-header-spacer {
    display: none !important;
  }

  /* Показываем бургер и меню на десктопе с новыми стилями */
  .burger {
    display: flex !important;
    position: fixed;
    top: 24px;
    left: 24px;
    width: 60px;
    height: 60px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .burger:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.2);
  }

  /* Контейнер для полосок бургера */
  .burger-lines {
    display: flex;
    flex-direction: column;
    gap: 3px;
    align-items: center;
    justify-content: center;
    height: 20px;
    width: 20px;
    position: relative;
  }

  .burger-lines span {
    width: 20px;
    height: 2px;
    background: #fff;
    border-radius: 1px;
    transform-origin: center;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
    position: relative;
    display: block;
  }

  .burger span:not(.burger-text) {
    width: 20px;
    height: 2px;
    background: #fff;
    margin: 1px 0;
    border-radius: 1px;
    transform-origin: center;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
    position: relative;
    display: block;
  }

  .burger-text {
    display: block;
    color: #fff;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 4px;
    line-height: 1;
  }

  .dropdown-menu {
    position: fixed;
    top: 80px;
    left: 24px;
    min-width: 200px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    padding: 16px;
    z-index: 1500;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    display: none;
    flex-direction: column;
    gap: 12px;
  }

  .dropdown-menu.active {
    display: flex;
    opacity: 1;
    transform: translateY(0);
  }

  .dropdown-menu a {
    color: #fff;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background 0.2s ease;
  }

  .dropdown-menu a:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.burger,
.burger:focus,
.burger:active {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
} 